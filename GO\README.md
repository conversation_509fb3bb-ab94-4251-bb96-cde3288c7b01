# Command Monitor (cmdmonitor)

Linux长命令监控服务 - 自动监控运行超过指定时间的命令，并在完成时发送微信通知。

## 功能特性

- 🔍 **智能监控**: 自动检测运行超过5分钟的长命令
- 📱 **微信通知**: 企业微信机器人推送详细完成报告
- 🐳 **Docker支持**: 监控主机进程和Docker容器内进程
- 💾 **本地存储**: SQLite数据库持久化存储监控历史
- ⚡ **轻量高效**: Go语言开发，资源占用低
- 🔧 **简单部署**: systemd服务，环境变量配置

## 快速开始

### 1. 编译安装

```bash
# 克隆项目
git clone <repository-url>
cd cmdmonitor

# 编译
go build -o cmdmonitor cmd/main.go

# 安装
sudo ./scripts/install.sh
```

### 2. 配置

```bash
# 复制配置文件
sudo cp configs/config.env.example /etc/cmdmonitor/config.env

# 编辑配置
sudo nano /etc/cmdmonitor/config.env
```

必须配置的环境变量：
- `WECHAT_WEBHOOK_URL`: 企业微信机器人Webhook URL

### 3. 启动服务

```bash
# 启动服务
sudo systemctl start cmdmonitor

# 设置开机自启
sudo systemctl enable cmdmonitor

# 查看状态
sudo systemctl status cmdmonitor

# 查看日志
sudo journalctl -u cmdmonitor -f
```

## 配置说明

### 企业微信机器人设置

1. 在企业微信群中添加机器人
2. 获取Webhook URL
3. 配置到 `WECHAT_WEBHOOK_URL` 环境变量

### 主要配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `MONITOR_THRESHOLD_MINUTES` | 5 | 监控阈值（分钟） |
| `SCAN_INTERVAL_SECONDS` | 30 | 扫描间隔（秒） |
| `MONITOR_DOCKER_ENABLED` | true | 是否监控Docker容器 |
| `MAX_MONITORED_PROCESSES` | 50 | 最大同时监控进程数 |

## 通知示例

```
🔔 长命令执行完成通知

✅ 成功 命令: python train_model.py --epochs 100
⏱️ 执行时长: 2小时15分钟
📊 退出码: 0
💾 内存使用: 8.5GB
🖥️ CPU使用: 85.2%
📍 容器: ml-training-container
⏰ 完成时间: 2025-01-17 15:30:25
```

## 项目结构

```
cmdmonitor/
├── cmd/                    # 主程序入口
├── internal/               # 内部模块
│   ├── monitor/           # 进程监控
│   ├── notification/      # 通知推送
│   ├── storage/          # 数据存储
│   └── config/           # 配置管理
├── pkg/                   # 公共工具
├── scripts/              # 部署脚本
├── configs/              # 配置文件
└── README.md
```

## 开发

### 本地开发

```bash
# 安装依赖
go mod tidy

# 运行
go run cmd/main.go

# 测试
go test ./...
```

### 构建

```bash
# 本地构建
make build

# 交叉编译
make build-linux
```

## 故障排除

### 常见问题

1. **权限不足**: 确保以root权限运行，或配置适当的sudo权限
2. **Docker连接失败**: 检查Docker socket路径和权限
3. **微信通知失败**: 验证Webhook URL和网络连接

### 日志查看

```bash
# 查看服务日志
sudo journalctl -u cmdmonitor -f

# 查看应用日志
sudo tail -f /var/log/cmdmonitor.log
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
