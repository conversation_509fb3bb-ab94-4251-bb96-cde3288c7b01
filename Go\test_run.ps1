# Command Monitor 测试脚本

Write-Host "=== Command Monitor 测试脚本 ===" -ForegroundColor Green

# 设置环境变量
$env:WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=test_key_here"
$env:WECHAT_DEFAULT_CONTACT = "@all"
$env:MONITOR_THRESHOLD_MINUTES = "1"
$env:SCAN_INTERVAL_SECONDS = "10"
$env:STORAGE_PATH = "./test_data.db"
$env:LOG_LEVEL = "debug"
$env:LOG_PATH = "./cmdmonitor.log"
$env:IGNORE_PROCESSES = "systemd,kthreadd,ksoftirqd"
$env:MONITOR_SYSTEM_PROCESSES = "false"
$env:MAX_MONITORED_PROCESSES = "10"
$env:MONITOR_DOCKER_ENABLED = "false"

Write-Host "配置环境变量完成" -ForegroundColor Yellow

# 检查二进制文件
if (Test-Path "build/cmdmonitor.exe") {
    Write-Host "找到二进制文件: build/cmdmonitor.exe" -ForegroundColor Green
} else {
    Write-Host "错误: 找不到二进制文件，请先运行编译" -ForegroundColor Red
    exit 1
}

Write-Host "注意: 由于这是测试环境，微信通知可能会失败（需要真实的Webhook URL）" -ForegroundColor Yellow
Write-Host "程序将在前台运行，按 Ctrl+C 停止" -ForegroundColor Yellow
Write-Host ""

# 运行程序
Write-Host "启动 Command Monitor..." -ForegroundColor Green
& "./build/cmdmonitor.exe"
