package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// Config 应用配置
type Config struct {
	// 企业微信配置
	WeChatWebhookURL    string `json:"wechat_webhook_url"`
	WeChatDefaultContact string `json:"wechat_default_contact"`

	// 监控配置
	MonitorThresholdMinutes int `json:"monitor_threshold_minutes"`
	ScanIntervalSeconds     int `json:"scan_interval_seconds"`
	MaxMonitoredProcesses   int `json:"max_monitored_processes"`

	// Docker配置
	DockerSocket        string `json:"docker_socket"`
	MonitorDockerEnabled bool   `json:"monitor_docker_enabled"`

	// 存储配置
	StoragePath string `json:"storage_path"`

	// 日志配置
	LogLevel string `json:"log_level"`
	LogPath  string `json:"log_path"`

	// 进程过滤配置
	IgnoreProcesses      []string `json:"ignore_processes"`
	MonitorSystemProcesses bool     `json:"monitor_system_processes"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		WeChatWebhookURL:       "",
		WeChatDefaultContact:   "@all",
		MonitorThresholdMinutes: 5,
		ScanIntervalSeconds:    30,
		MaxMonitoredProcesses:  50,
		DockerSocket:           "/var/run/docker.sock",
		MonitorDockerEnabled:   true,
		StoragePath:           "/var/lib/cmdmonitor/data.db",
		LogLevel:              "info",
		LogPath:               "/var/log/cmdmonitor.log",
		IgnoreProcesses: []string{
			"systemd", "kthreadd", "ksoftirqd", "migration", 
			"rcu_", "watchdog", "systemd-", "dbus", 
			"NetworkManager", "sshd", "chronyd", "rsyslog",
		},
		MonitorSystemProcesses: false,
	}
}

// LoadFromEnv 从环境变量加载配置
func LoadFromEnv() (*Config, error) {
	config := DefaultConfig()

	// 企业微信配置
	if url := os.Getenv("WECHAT_WEBHOOK_URL"); url != "" {
		config.WeChatWebhookURL = url
	}

	if contact := os.Getenv("WECHAT_DEFAULT_CONTACT"); contact != "" {
		config.WeChatDefaultContact = contact
	}

	// 监控配置
	if threshold := os.Getenv("MONITOR_THRESHOLD_MINUTES"); threshold != "" {
		if val, err := strconv.Atoi(threshold); err == nil && val > 0 {
			config.MonitorThresholdMinutes = val
		}
	}

	if interval := os.Getenv("SCAN_INTERVAL_SECONDS"); interval != "" {
		if val, err := strconv.Atoi(interval); err == nil && val > 0 {
			config.ScanIntervalSeconds = val
		}
	}

	if maxProc := os.Getenv("MAX_MONITORED_PROCESSES"); maxProc != "" {
		if val, err := strconv.Atoi(maxProc); err == nil && val > 0 {
			config.MaxMonitoredProcesses = val
		}
	}

	// Docker配置
	if socket := os.Getenv("DOCKER_SOCKET"); socket != "" {
		config.DockerSocket = socket
	}

	if dockerEnabled := os.Getenv("MONITOR_DOCKER_ENABLED"); dockerEnabled != "" {
		config.MonitorDockerEnabled = strings.ToLower(dockerEnabled) == "true"
	}

	// 存储配置
	if storagePath := os.Getenv("STORAGE_PATH"); storagePath != "" {
		config.StoragePath = storagePath
	}

	// 日志配置
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = strings.ToLower(logLevel)
	}

	if logPath := os.Getenv("LOG_PATH"); logPath != "" {
		config.LogPath = logPath
	}

	// 进程过滤配置
	if ignoreProc := os.Getenv("IGNORE_PROCESSES"); ignoreProc != "" {
		processes := strings.Split(ignoreProc, ",")
		var cleanProcesses []string
		for _, proc := range processes {
			if trimmed := strings.TrimSpace(proc); trimmed != "" {
				cleanProcesses = append(cleanProcesses, trimmed)
			}
		}
		if len(cleanProcesses) > 0 {
			config.IgnoreProcesses = cleanProcesses
		}
	}

	if monitorSys := os.Getenv("MONITOR_SYSTEM_PROCESSES"); monitorSys != "" {
		config.MonitorSystemProcesses = strings.ToLower(monitorSys) == "true"
	}

	return config, nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 必需的配置检查
	if c.WeChatWebhookURL == "" {
		return fmt.Errorf("WECHAT_WEBHOOK_URL 是必需的配置")
	}

	// 验证微信Webhook URL格式
	if !strings.HasPrefix(c.WeChatWebhookURL, "https://qyapi.weixin.qq.com/") {
		return fmt.Errorf("WECHAT_WEBHOOK_URL 格式不正确，应该以 https://qyapi.weixin.qq.com/ 开头")
	}

	// 验证数值范围
	if c.MonitorThresholdMinutes < 1 {
		return fmt.Errorf("MONITOR_THRESHOLD_MINUTES 必须大于0")
	}

	if c.ScanIntervalSeconds < 10 {
		return fmt.Errorf("SCAN_INTERVAL_SECONDS 必须大于等于10秒")
	}

	if c.MaxMonitoredProcesses < 1 {
		return fmt.Errorf("MAX_MONITORED_PROCESSES 必须大于0")
	}

	// 验证日志级别
	validLogLevels := []string{"trace", "debug", "info", "warn", "error", "fatal", "panic"}
	isValidLogLevel := false
	for _, level := range validLogLevels {
		if c.LogLevel == level {
			isValidLogLevel = true
			break
		}
	}
	if !isValidLogLevel {
		return fmt.Errorf("LOG_LEVEL 必须是以下值之一: %s", strings.Join(validLogLevels, ", "))
	}

	return nil
}

// GetLogLevel 获取日志级别
func (c *Config) GetLogLevel() logrus.Level {
	switch strings.ToLower(c.LogLevel) {
	case "trace":
		return logrus.TraceLevel
	case "debug":
		return logrus.DebugLevel
	case "info":
		return logrus.InfoLevel
	case "warn", "warning":
		return logrus.WarnLevel
	case "error":
		return logrus.ErrorLevel
	case "fatal":
		return logrus.FatalLevel
	case "panic":
		return logrus.PanicLevel
	default:
		return logrus.InfoLevel
	}
}

// String 返回配置的字符串表示（隐藏敏感信息）
func (c *Config) String() string {
	// 隐藏敏感的Webhook URL
	maskedURL := c.WeChatWebhookURL
	if len(maskedURL) > 20 {
		maskedURL = maskedURL[:20] + "***"
	}

	return fmt.Sprintf(`配置信息:
  微信Webhook: %s
  默认联系人: %s
  监控阈值: %d分钟
  扫描间隔: %d秒
  最大监控进程数: %d
  Docker监控: %t
  Docker Socket: %s
  存储路径: %s
  日志级别: %s
  日志路径: %s
  忽略进程: %v
  监控系统进程: %t`,
		maskedURL,
		c.WeChatDefaultContact,
		c.MonitorThresholdMinutes,
		c.ScanIntervalSeconds,
		c.MaxMonitoredProcesses,
		c.MonitorDockerEnabled,
		c.DockerSocket,
		c.StoragePath,
		c.LogLevel,
		c.LogPath,
		c.IgnoreProcesses,
		c.MonitorSystemProcesses,
	)
}

// GetThresholdDuration 获取监控阈值时间间隔
func (c *Config) GetThresholdDuration() time.Duration {
	return time.Duration(c.MonitorThresholdMinutes) * time.Minute
}

// GetScanInterval 获取扫描间隔时间间隔
func (c *Config) GetScanInterval() time.Duration {
	return time.Duration(c.ScanIntervalSeconds) * time.Second
}
