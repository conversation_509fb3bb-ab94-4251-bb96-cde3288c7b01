# Linux命令监控服务需求调研

## 项目概述
开发一个Go语言的Linux后台服务，监控长时间运行的前台命令执行状态，并在命令完成时发送通知到手机。

## 核心功能需求问题

### 1. 命令监控方式
- **Q1**: 您希望如何指定需要监控的命令？
  - [ ] 通过配置文件预设命令列表
  - [ ] 通过命令行参数动态添加
  - [ ] 通过Web界面管理
  - [ ] 通过特殊的命令前缀（如 `monitor your_command`）
  - [ ] 其他方式：_____________

- **Q2**: 需要监控哪些类型的命令？
  - [ ] 任意shell命令
  - [ ] 特定的应用程序（如编译、备份、数据处理等）
  - [ ] 脚本文件执行
  - [ ] 具体示例：_通过时长去判断如果 大于五分钟 那么则判断为长命令 然后推送____________

- **Q3**: 是否需要监控命令的实时输出？
  - [x] 关心完成状态
  - [ ] 需要捕获输出日志
  - [ ] 需要监控进度信息
  - [x] 需要错误信息捕获

### 2. 监控触发机制
- **Q4**: 如何启动命令监控？
  - [ ] 手动启动每个监控任务
  - [x] 自动检测新启动的进程
  - [ ] 通过包装脚本启动命令
  - [ ] 监控特定目录下的进程
  - [ ] 其他方式：_____________
  可能还有docker 里面的命令 也需要监控

- **Q5**: 是否需要监控已经在运行的进程？
  - [ ] 是，需要能够attach到现有进程
  - [ ] 否，只监控新启动的命令
  - [ ] 可选功能
   还是根据时间长度去判断

### 3. 通知推送方式
- **Q6**: 您偏好哪种手机通知方式？
  - [x] 微信（企业微信机器人/个人微信）
  - [ ] 钉钉机器人
  - [ ] Telegram Bot
  - [ ] 短信通知
  - [ ] 邮件通知
  - [ ] 自定义Webhook
  - [ ] 多种方式同时支持
    要有一个默认联系人

- **Q7**: 通知内容需要包含哪些信息？
  - [x] 命令名称和参数
  - [x] 执行时长
  - [x] 退出状态码
  - [x] 执行结果摘要
  - [x] 错误信息（如果失败）
  - [x] 系统资源使用情况
  - [ ] 其他：_____________

### 4. 服务部署和管理
- **Q8**: 服务部署方式偏好？
  - [x] systemd服务
  - [ ] Docker容器
  - [ ] 直接二进制文件
  - [ ] 其他：_____________

- **Q9**: 是否需要Web管理界面？
  - [ ] 是，用于查看监控状态和历史
  - [ ] 是，用于配置和管理
  - [x] 否，命令行管理即可
  - [ ] 简单的状态页面即可

- **Q10**: 配置管理方式？
  - [ ] 配置文件（YAML/JSON/TOML）
  - [x] 环境变量
  - [ ] 命令行参数
  - [ ] 数据库存储
  - [ ] 混合方式
  要有一个默认联系人

### 5. 高级功能需求
- **Q11**: 是否需要以下高级功能？
  - [ ] 命令执行超时检测
  - [ ] 资源使用监控（CPU/内存）
  - [ ] 命令执行历史记录
  - [ ] 失败重试机制
  - [ ] 命令依赖关系管理
  - [ ] 定时任务监控
  - [ ] 集群多机器监控
暂时不需要
- **Q12**: 性能和规模要求？
  - 预计同时监控多少个命令：_5个以内吧  优先docker内的命令____________
  - 单个命令最长执行时间：_无上限____________
  - 是否需要持久化存储：_liunx本地存储____________

### 6. 安全和权限
- **Q13**: 安全考虑？
  - [ ] 需要用户认证
  - [ ] 需要命令执行权限控制
  - [ ] 需要通知内容加密
  - [ ] 需要访问日志审计
  - [ ] 其他安全要求：_______否______

### 7. 技术细节
- **Q14**: Linux发行版和版本？
  - 目标系统：_小红帽 乌班图20 22  __主流系统即可__________
  - Go版本要求：_____无________
  - 其他依赖：_____________

- **Q15**: 是否有现有的监控工具集成需求？
  - [ ] 需要与现有监控系统集成
  - [ ] 需要导出metrics到Prometheus等
  - [x] 独立运行即可

## 使用场景示例
请提供一些具体的使用场景，例如：
- 场景1：_大模型测试____________
- 场景2：_大模型文件解压 传送 ____________
- 场景3：___。。__________

## 优先级排序
请对以上功能按重要性排序（1-5，5为最重要）：
- 基础命令监控：_5__
- 手机通知推送：4___
- Web管理界面：___
- 历史记录查询：_2__
- 高级监控功能：_3__

---
**请逐一回答以上问题，我们将基于您的回答设计详细的技术方案。**
