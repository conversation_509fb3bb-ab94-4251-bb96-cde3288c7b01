# 企业微信配置
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_webhook_key_here
WECHAT_DEFAULT_CONTACT=@all

# 监控配置
MONITOR_THRESHOLD_MINUTES=5
SCAN_INTERVAL_SECONDS=30
STORAGE_PATH=/var/lib/cmdmonitor/data.db

# Docker配置
DOCKER_SOCKET=/var/run/docker.sock
MONITOR_DOCKER_ENABLED=true

# 日志配置
LOG_LEVEL=info
LOG_PATH=/var/log/cmdmonitor.log

# 进程过滤配置（可选）
# 忽略的进程名称，用逗号分隔
IGNORE_PROCESSES=systemd,kthreadd,ksoftirqd,migration,rcu_,watchdog

# 监控范围配置
# 是否监控系统进程（UID < 1000）
MONITOR_SYSTEM_PROCESSES=false

# 最大同时监控进程数
MAX_MONITORED_PROCESSES=50
